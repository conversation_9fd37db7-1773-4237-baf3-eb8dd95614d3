// macOS-inspired Typography System
// Maintains Rubik font preference while adding system font fallbacks

export interface TypographyStyles {
  title1: string;
  title2: string;
  title3: string;
  body: string;
  bodySecondary: string;
  caption: string;
  footnote: string;
  button: string;
  label: string;
}

export const Typography: TypographyStyles = {
  // Large titles (24px)
  title1: "text-3xl font-semibold tracking-tight text-macos-text-primary",

  // Section headers (20px)
  title2: "text-2xl font-medium tracking-tight text-macos-text-primary",

  // Subsection headers (16px)
  title3: "text-xl font-medium text-macos-text-primary",

  // Body text (13px)
  body: "text-base text-macos-text-primary leading-relaxed",

  // Secondary text (13px)
  bodySecondary: "text-base text-macos-text-secondary leading-relaxed",

  // Small text (12px)
  caption: "text-sm text-macos-text-secondary",

  // Tiny text (11px)
  footnote: "text-xs text-macos-text-tertiary",

  // Button text
  button: "text-base font-medium",

  // Label text
  label: "text-sm font-medium text-macos-text-primary",
};

// Animation easing curves
export interface EasingCurves {
  smooth: number[];
  snappy: number[];
  gentle: number[];
}

export const easings: EasingCurves = {
  smooth: [0.25, 0.1, 0.25, 1],
  snappy: [0.4, 0, 0.2, 1],
  gentle: [0.16, 1, 0.3, 1],
};

// Common animation variants for Framer Motion
export interface AnimationVariant {
  initial: Record<string, any>;
  animate: Record<string, any>;
  exit: Record<string, any>;
  transition: Record<string, any>;
}

export const fadeIn: AnimationVariant = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 },
  transition: { duration: 0.3, ease: easings.smooth }
};

export const scaleIn: AnimationVariant = {
  initial: { opacity: 0, scale: 0.95 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.95 },
  transition: { duration: 0.2, ease: easings.snappy }
};

export const slideIn: AnimationVariant = {
  initial: { opacity: 0, x: -20 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: 20 },
  transition: { duration: 0.3, ease: easings.smooth }
};

export const slideInFromRight: AnimationVariant = {
  initial: { opacity: 0, x: 20 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -20 },
  transition: { duration: 0.3, ease: easings.smooth }
};

// Staggered list animations
export interface ListVariants {
  animate: {
    transition: {
      staggerChildren: number;
    };
  };
}

export interface ItemVariants {
  initial: Record<string, any>;
  animate: Record<string, any>;
}

export const listVariants: ListVariants = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
};

export const itemVariants: ItemVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 }
};

// Hover and interactive states
export interface InteractiveStates {
  base: string;
  hover: string;
  active: string;
  focus: string;
}

export const interactiveStates: InteractiveStates = {
  base: "transition-all duration-200 ease-out",
  hover: "hover:bg-macos-elevated hover:transform hover:translate-y-[-1px]",
  active: "active:bg-macos-border/30 active:transform active:translate-y-0",
  focus: "focus:outline-none focus:ring-2 focus:ring-system-blue/50",
};
