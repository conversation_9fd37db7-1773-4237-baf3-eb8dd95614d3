// Browser-compatible logger for React frontend
// Uses console with Gen-Z emojis and styling
// TypeScript version with enhanced functionality

import type { LogLevel } from '@/types';

interface LogMessage {
  level: LogLevel;
  scope?: string;
  message: string;
  args?: any[];
}

class BrowserLogger {
  private scope: string;
  private isElectron: boolean;

  constructor(scope = '') {
    this.scope = scope;
    this.isElectron = typeof window !== 'undefined' && window.electronAPI !== undefined;
  }

  private async _formatMessage(emoji: string, label: string, message: string, ...args: any[]): Promise<void> {
    const timestamp = new Date().toLocaleTimeString();
    const scopeStr = this.scope ? `[${this.scope}]` : '';
    const prefix = `${scopeStr} › ${emoji} ${label}`;

    // Log to browser console
    if (typeof message === 'object') {
      console.log(`${prefix}`, message, ...args);
    } else {
      console.log(`${prefix} ${message}`, ...args);
    }

    // Also send to Electron main process for server-side logging
    if (this.isElectron && window.electronAPI) {
      try {
        await window.electronAPI.logMessage({
          level: label as LogLevel,
          scope: this.scope || undefined,
          message: typeof message === 'string' ? message : JSON.stringify(message),
          args: args.length > 0 ? args : undefined,
        });
      } catch (error) {
        console.warn('Failed to send log to main process:', error);
      }
    }
  }

  // Success and positive actions
  async success(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('✨', 'success', message, ...args);
  }

  async complete(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('🎉', 'complete', message, ...args);
  }

  // Information and status
  async info(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('💡', 'info', message, ...args);
  }

  async note(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('📝', 'note', message, ...args);
  }

  // Process and loading states
  async pending(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('⏳', 'pending', message, ...args);
  }

  async await(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('⌛', 'await', message, ...args);
  }

  async watch(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('👀', 'watch', message, ...args);
  }

  async start(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('🚀', 'start', message, ...args);
  }

  // Warnings and cautions
  async warn(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('⚠️', 'warn', message, ...args);
  }

  async pause(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('⏸️', 'pause', message, ...args);
  }

  // Errors and failures
  async error(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('💥', 'error', message, ...args);
  }

  async fatal(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('💀', 'fatal', message, ...args);
  }

  // Debug and development
  async debug(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('🐛', 'debug', message, ...args);
  }

  // Special FileDuck specific loggers
  async fileduck(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('🦆', 'fileduck', message, ...args);
  }

  async app(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('📱', 'app', message, ...args);
  }

  async install(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('📦', 'install', message, ...args);
  }

  async uninstall(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('🗑️', 'uninstall', message, ...args);
  }

  async python(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('🐍', 'python', message, ...args);
  }

  async file(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('📄', 'file', message, ...args);
  }

  async api(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('🌐', 'api', message, ...args);
  }

  async database(message: string, ...args: any[]): Promise<void> {
    await this._formatMessage('🗄️', 'database', message, ...args);
  }

  // Create scoped logger
  scope(scopeName: string): BrowserLogger {
    return new BrowserLogger(scopeName);
  }
}

// Create the main logger instance
const logger = new BrowserLogger();

// Export both the configured logger and the class for custom instances
export {
  logger,
  BrowserLogger
};

export const createLogger = (scope: string): BrowserLogger => logger.scope(scope);
