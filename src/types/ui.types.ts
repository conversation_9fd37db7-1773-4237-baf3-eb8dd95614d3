import type { ReactNode, ComponentProps } from 'react';

// Common UI component props
export interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
  id?: string;
  'data-testid'?: string;
}

// Button variants (for shadcn/ui)
export interface ButtonVariants {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
}

// Card component props
export interface CardProps extends BaseComponentProps {
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  hover?: boolean;
}

// Modal/Dialog props
export interface DialogProps extends BaseComponentProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  description?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
}

// Toast notification types
export interface ToastProps {
  id?: string;
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive' | 'success' | 'warning';
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// Progress indicator props
export interface ProgressProps extends BaseComponentProps {
  value: number;
  max?: number;
  variant?: 'default' | 'success' | 'warning' | 'error';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  label?: string;
}

// File upload component props
export interface FileUploadProps extends BaseComponentProps {
  accept?: string;
  multiple?: boolean;
  maxSize?: number;
  onFileSelect: (files: File[]) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  placeholder?: string;
  variant?: 'default' | 'compact' | 'dropzone';
}

// Loading spinner props
export interface LoadingSpinnerProps extends BaseComponentProps {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'dots' | 'pulse';
  text?: string;
}

// Icon props
export interface IconProps extends BaseComponentProps {
  name: string;
  size?: number | 'sm' | 'md' | 'lg';
  color?: string;
  strokeWidth?: number;
}

// Layout component props
export interface LayoutProps extends BaseComponentProps {
  sidebar?: ReactNode;
  header?: ReactNode;
  footer?: ReactNode;
  sidebarWidth?: number;
  sidebarCollapsed?: boolean;
  onSidebarToggle?: () => void;
}

// Form field props
export interface FormFieldProps extends BaseComponentProps {
  label?: string;
  description?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
}

// Input component props
export interface InputProps extends FormFieldProps, Omit<ComponentProps<'input'>, 'size'> {
  variant?: 'default' | 'filled' | 'outlined';
  size?: 'sm' | 'md' | 'lg';
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
}

// Select component props
export interface SelectProps extends FormFieldProps {
  options: Array<{
    value: string;
    label: string;
    disabled?: boolean;
  }>;
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  searchable?: boolean;
  multiple?: boolean;
}

// Checkbox component props
export interface CheckboxProps extends FormFieldProps, Omit<ComponentProps<'input'>, 'type'> {
  checked?: boolean;
  onCheckedChange: (checked: boolean) => void;
  indeterminate?: boolean;
}

// Switch component props
export interface SwitchProps extends FormFieldProps {
  checked?: boolean;
  onCheckedChange: (checked: boolean) => void;
  size?: 'sm' | 'md' | 'lg';
}

// Slider component props
export interface SliderProps extends FormFieldProps {
  value: number[];
  onValueChange: (value: number[]) => void;
  min?: number;
  max?: number;
  step?: number;
  range?: boolean;
  marks?: Array<{ value: number; label?: string }>;
}

// Tabs component props
export interface TabsProps extends BaseComponentProps {
  value: string;
  onValueChange: (value: string) => void;
  orientation?: 'horizontal' | 'vertical';
  variant?: 'default' | 'pills' | 'underline';
}

// Table component props
export interface TableProps extends BaseComponentProps {
  data: any[];
  columns: Array<{
    key: string;
    label: string;
    sortable?: boolean;
    render?: (value: any, row: any) => ReactNode;
  }>;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  onSort?: (key: string, order: 'asc' | 'desc') => void;
  loading?: boolean;
  emptyMessage?: string;
}

// Pagination props
export interface PaginationProps extends BaseComponentProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showFirstLast?: boolean;
  showPrevNext?: boolean;
  maxVisiblePages?: number;
}
