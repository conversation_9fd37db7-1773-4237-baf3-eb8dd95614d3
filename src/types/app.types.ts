import type { ReactNode } from 'react';
import type { AppConfig, StoreApp, FileData } from '@/schemas';

// Navigation types
export type TabType = 'home' | 'store' | 'my-apps' | 'preferences' | 'debug';

export interface NavigationTab {
  id: TabType;
  label: string;
  icon: ReactNode;
  path: string;
  disabled?: boolean;
}

// App state types
export interface AppState {
  currentTab: TabType;
  sidebarVisible: boolean;
  loading: boolean;
  error: string | null;
  installedApps: AppConfig[];
  storeApps: StoreApp[];
}

// Store data context types
export interface StoreDataContextType {
  apps: StoreApp[];
  loading: boolean;
  error: string | null;
  refreshApps: () => Promise<void>;
  getAppById: (id: string) => StoreApp | undefined;
  getAppsByCategory: (category: string) => StoreApp[];
  getFeaturedApps: () => StoreApp[];
  getTrendingApps: () => StoreApp[];
  getNewApps: () => StoreApp[];
  getRecommendedApps: () => StoreApp[];
}

// App installation types
export interface InstallationState {
  [appId: string]: {
    status: 'idle' | 'downloading' | 'installing' | 'complete' | 'error';
    progress: number;
    error?: string;
  };
}

// Utility runner types
export interface UtilityRunnerState {
  utilityId: string;
  config: AppConfig | null;
  loading: boolean;
  error: string | null;
  files: FileData[];
  results: any[];
  processing: boolean;
}

// Blueprint renderer types
export interface BlueprintComponent {
  type: string;
  props?: Record<string, any>;
  children?: BlueprintComponent[];
  key?: string;
}

export interface BlueprintRendererProps {
  blueprint: BlueprintComponent[];
  utilityId: string;
  onFileSelect?: (files: FileData[]) => void;
  onConvert?: (config: Record<string, any>) => void;
}

// Error boundary types
export interface ErrorInfo {
  componentStack: string;
}

export interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

// Debug types
export interface DebugInfo {
  electronVersion: string;
  nodeVersion: string;
  chromeVersion: string;
  platform: string;
  arch: string;
  userDataPath: string;
  installedApps: number;
  memoryUsage: NodeJS.MemoryUsage;
}

// Theme types
export type ThemeMode = 'light' | 'dark' | 'system';

// File processing types
export interface FileProcessingOptions {
  maxFileSize?: number;
  allowedTypes?: string[];
  multiple?: boolean;
  outputFormat?: string;
  quality?: number;
  compression?: boolean;
}

// Search and filter types
export interface SearchFilters {
  query: string;
  category?: string;
  tags?: string[];
  sortBy?: 'name' | 'date' | 'popularity' | 'rating';
  sortOrder?: 'asc' | 'desc';
}

// Analytics types
export interface AnalyticsEvent {
  event: string;
  properties?: Record<string, any>;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
}
