import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'electron';
import type { 
  FileData, 
  FileDialogOptions, 
  UtilityExecutionConfig, 
  UtilityResult,
  AppConfig,
  InstallationProgress 
} from '@/schemas';

// Electron API interface
export interface ElectronAPI {
  // IPC communication
  ipcRenderer: Ipc<PERSON><PERSON><PERSON>;
  
  // Utility management
  getInstalledUtilities: () => Promise<AppConfig[]>;
  installUtility: (downloadUrl: string) => Promise<InstallationProgress>;
  installUtilityFromLocal: (localZipPath: string) => Promise<InstallationProgress>;
  uninstallUtility: (utilityId: string) => Promise<boolean>;
  runUtility: (utilityId: string, fileData?: FileData[], config?: Record<string, any>) => Promise<UtilityResult>;
  
  // File management
  showOpenDialog: (options?: FileDialogOptions) => Promise<{ canceled: boolean; filePaths: string[] }>;
  showSaveDialog: (options?: FileDialogOptions) => Promise<{ canceled: boolean; filePath?: string }>;
  readFile: (filePath: string) => Promise<FileData>;
  writeFile: (filePath: string, data: string | Buffer, options?: any) => Promise<boolean>;
  openFolder: (folderPath: string) => Promise<{ success: boolean; error?: string }>;
  
  // Python execution
  executePython: (pythonPath: string, functionName: string, ...args: any[]) => Promise<any>;
  processFilePython: (pythonPath: string, fileData: FileData, config?: Record<string, any>) => Promise<any>;
  processFilesPython: (pythonPath: string, filesData: FileData[], config?: Record<string, any>) => Promise<any[]>;
  
  // Database management (debug)
  getUtilityById: (id: string) => Promise<AppConfig | null>;
  deleteUtility: (id: string) => Promise<boolean>;
  clearDatabase: () => Promise<{ success: boolean }>;
  addUtility: (data: AppConfig) => Promise<{ success: boolean }>;
  updateUtility: (id: string, data: Partial<AppConfig>) => Promise<{ success: boolean }>;
  getUserDataPath: () => Promise<string>;
  
  // Development tools
  toggleDevtools: () => Promise<void>;
  
  // Logging
  logMessage: (logData: {
    level: LogLevel;
    scope?: string;
    message: string;
    args?: any[];
  }) => Promise<void>;
}

// Log levels
export type LogLevel = 
  | 'success' 
  | 'complete' 
  | 'info' 
  | 'note' 
  | 'pending' 
  | 'await' 
  | 'watch' 
  | 'start' 
  | 'warn' 
  | 'pause' 
  | 'error' 
  | 'fatal' 
  | 'debug' 
  | 'fileduck' 
  | 'app' 
  | 'install' 
  | 'uninstall' 
  | 'python' 
  | 'file' 
  | 'api' 
  | 'database';

// Window global interface
declare global {
  interface Window {
    electronAPI?: ElectronAPI;
  }
}

// Electron main process types
export interface MainProcessConfig {
  isDev: boolean;
  userDataPath: string;
  resourcesPath: string;
}

// IPC channel types
export type IpcChannels = 
  | 'get-installed-utilities'
  | 'install-utility'
  | 'install-utility-from-local'
  | 'uninstall-utility'
  | 'run-utility'
  | 'show-open-dialog'
  | 'show-save-dialog'
  | 'read-file'
  | 'write-file'
  | 'open-folder'
  | 'execute-python'
  | 'process-file-python'
  | 'process-files-python'
  | 'get-utility-by-id'
  | 'delete-utility'
  | 'clear-database'
  | 'add-utility'
  | 'update-utility'
  | 'get-user-data-path'
  | 'toggle-devtools'
  | 'log-message';
