import type { StoreApp } from '@/schemas';

// API base types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp?: string;
  requestId?: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  stack?: string;
}

// Store API types
export interface StoreApiResponse extends ApiResponse<StoreApp[]> {}

export interface AppDetailsApiResponse extends ApiResponse<StoreApp> {}

export interface InstallApiResponse extends ApiResponse<{
  downloadUrl: string;
  size: number;
  checksum?: string;
}> {}

// Analytics API types
export interface AnalyticsApiRequest {
  event: 'app_run' | 'app_install' | 'app_uninstall';
  app_id: string;
  user_id: string;
  timestamp?: string;
  metadata?: Record<string, any>;
}

export interface AnalyticsApiResponse extends ApiResponse<{
  tracked: boolean;
  eventId: string;
}> {}

// Search API types
export interface SearchApiRequest {
  query?: string;
  category?: string;
  tags?: string[];
  limit?: number;
  offset?: number;
  sortBy?: 'name' | 'downloads' | 'rating' | 'created_at';
  sortOrder?: 'asc' | 'desc';
}

export interface SearchApiResponse extends ApiResponse<{
  apps: StoreApp[];
  total: number;
  hasMore: boolean;
}> {}

// Categories API types
export interface CategoriesApiResponse extends ApiResponse<Array<{
  id: string;
  name: string;
  description?: string;
  icon?: string;
  appCount: number;
}>> {}

// Featured content API types
export interface FeaturedContentApiResponse extends ApiResponse<{
  trending: StoreApp[];
  popular: StoreApp[];
  new: StoreApp[];
  recommended: StoreApp[];
}> {}

// HTTP client configuration
export interface HttpClientConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
  retries?: number;
  retryDelay?: number;
}

// Request options
export interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  signal?: AbortSignal;
}

// API endpoints
export interface ApiEndpoints {
  apps: string;
  appDetails: (id: string) => string;
  install: (id: string) => string;
  analytics: string;
  search: string;
  categories: string;
  featured: string;
}

// API client interface
export interface ApiClient {
  get<T = any>(url: string, options?: RequestOptions): Promise<ApiResponse<T>>;
  post<T = any>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>>;
  put<T = any>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>>;
  delete<T = any>(url: string, options?: RequestOptions): Promise<ApiResponse<T>>;
  patch<T = any>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>>;
}

// Cache types
export interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
}

export interface CacheManager {
  get<T = any>(key: string): T | null;
  set<T = any>(key: string, data: T, ttl?: number): void;
  delete(key: string): void;
  clear(): void;
  has(key: string): boolean;
}

// Rate limiting types
export interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

export interface RateLimitState {
  requests: number;
  resetTime: number;
}
