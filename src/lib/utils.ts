import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import {
  debounce,
  throttle,
  isEmpty,
  isEqual,
  pick,
  omit,
  merge,
  cloneDeep,
  uniq,
  uniqBy,
  groupBy,
  sortBy,
  orderBy,
  chunk,
  flatten,
  compact,
  get,
  set,
  has
} from 'lodash';

export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs));
}

// Enhanced utility functions with Lodash
export const utils = {
  // Class name utilities
  cn,

  // Performance utilities
  debounce: debounce as typeof debounce,
  throttle: throttle as typeof throttle,

  // Object utilities
  isEmpty: isEmpty as typeof isEmpty,
  isEqual: isEqual as typeof isEqual,
  pick: pick as typeof pick,
  omit: omit as typeof omit,
  merge: merge as typeof merge,
  cloneDeep: cloneDeep as typeof cloneDeep,
  get: get as typeof get,
  set: set as typeof set,
  has: has as typeof has,

  // Array utilities
  uniq: uniq as typeof uniq,
  uniqBy: uniqBy as typeof uniqBy,
  groupBy: groupBy as typeof groupBy,
  sortBy: sortBy as typeof sortBy,
  orderBy: orderBy as typeof orderBy,
  chunk: chunk as typeof chunk,
  flatten: flatten as typeof flatten,
  compact: compact as typeof compact,

  // Type guards
  isString: (value: unknown): value is string => typeof value === 'string',
  isNumber: (value: unknown): value is number => typeof value === 'number' && !isNaN(value),
  isBoolean: (value: unknown): value is boolean => typeof value === 'boolean',
  isArray: Array.isArray,
  isObject: (value: unknown): value is Record<string, unknown> =>
    value !== null && typeof value === 'object' && !Array.isArray(value),
  isFunction: (value: unknown): value is Function => typeof value === 'function',

  // String utilities
  capitalize: (str: string): string => str.charAt(0).toUpperCase() + str.slice(1),
  truncate: (str: string, length: number): string =>
    str.length > length ? str.slice(0, length) + '...' : str,
  slugify: (str: string): string =>
    str.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),

  // Number utilities
  formatBytes: (bytes: number, decimals = 2): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  },

  formatNumber: (num: number): string => new Intl.NumberFormat().format(num),

  // Date utilities
  formatDate: (date: Date | string | number): string => {
    const d = new Date(date);
    return d.toLocaleDateString();
  },

  formatDateTime: (date: Date | string | number): string => {
    const d = new Date(date);
    return d.toLocaleString();
  },

  // Validation utilities
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  isValidUrl: (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  // File utilities
  getFileExtension: (filename: string): string => {
    return filename.split('.').pop()?.toLowerCase() || '';
  },

  getMimeType: (filename: string): string => {
    const ext = utils.getFileExtension(filename);
    const mimeTypes: Record<string, string> = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'pdf': 'application/pdf',
      'txt': 'text/plain',
      'csv': 'text/csv',
      'json': 'application/json',
      'xml': 'application/xml',
      'zip': 'application/zip',
    };
    return mimeTypes[ext] || 'application/octet-stream';
  },

  // Error handling utilities
  safeJsonParse: <T = unknown>(json: string, fallback: T): T => {
    try {
      return JSON.parse(json);
    } catch {
      return fallback;
    }
  },

  // Promise utilities
  sleep: (ms: number): Promise<void> => new Promise(resolve => setTimeout(resolve, ms)),

  timeout: <T>(promise: Promise<T>, ms: number): Promise<T> => {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Operation timed out')), ms)
      )
    ]);
  },
};
