import { z } from 'zod';

// User preferences schema
export const UserPreferencesSchema = z.object({
  theme: z.enum(['light', 'dark', 'system']).default('system'),
  language: z.string().default('en'),
  autoUpdate: z.boolean().default(true),
  telemetry: z.boolean().default(false),
  defaultOutputPath: z.string().optional(),
  maxConcurrentJobs: z.number().min(1).max(10).default(3),
  logLevel: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
});

// Electron window state schema
export const WindowStateSchema = z.object({
  width: z.number().min(800).default(1200),
  height: z.number().min(600).default(800),
  x: z.number().optional(),
  y: z.number().optional(),
  maximized: z.boolean().default(false),
  minimized: z.boolean().default(false),
  fullscreen: z.boolean().default(false),
});

// Application settings schema
export const AppSettingsSchema = z.object({
  userPreferences: UserPreferencesSchema,
  windowState: WindowStateSchema,
  lastOpenedTab: z.string().optional(),
  recentFiles: z.array(z.string()).max(10).default([]),
  installedApps: z.array(z.string()).default([]),
});

// Python environment configuration
export const PythonConfigSchema = z.object({
  pythonPath: z.string().optional(),
  pipPath: z.string().optional(),
  virtualEnvPath: z.string().optional(),
  packages: z.array(z.string()).default([]),
  version: z.string().optional(),
});

// Export types
export type UserPreferences = z.infer<typeof UserPreferencesSchema>;
export type WindowState = z.infer<typeof WindowStateSchema>;
export type AppSettings = z.infer<typeof AppSettingsSchema>;
export type PythonConfig = z.infer<typeof PythonConfigSchema>;
