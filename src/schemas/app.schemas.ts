import { z } from 'zod';

// App configuration schema
export const AppConfigSchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1),
  description: z.string().optional(),
  version: z.string().default('1.0.0'),
  author: z.string().optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).default([]),
  icon: z.string().optional(),
  language: z.enum(['python', 'javascript', 'rust']).optional(),
  main: z.string().optional(),
  requirements: z.array(z.string()).optional(),
  runtimeType: z.enum(['python', 'python-spawn', 'wasm']).optional(),
  ui: z.any().optional(), // React component configuration
});

// App metadata schema
export const AppMetadataSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  version: z.string(),
  author: z.string().optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).default([]),
  icon: z.string().optional(),
  installedAt: z.date().optional(),
  lastUsed: z.date().optional(),
  usageCount: z.number().default(0),
});

// Store app data schema
export const StoreAppSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  version: z.string(),
  author: z.string().optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).default([]),
  icon: z.string().optional(),
  installer_url: z.string().url(),
  downloads: z.number().default(0),
  rating: z.number().min(0).max(5).optional(),
  screenshots: z.array(z.string().url()).optional(),
  featured: z.boolean().default(false),
  trending: z.boolean().default(false),
  new: z.boolean().default(false),
  recommended: z.boolean().default(false),
});

// Installation progress schema
export const InstallationProgressSchema = z.object({
  appId: z.string(),
  status: z.enum(['downloading', 'extracting', 'installing', 'complete', 'error']),
  progress: z.number().min(0).max(100),
  message: z.string().optional(),
  error: z.string().optional(),
});

// Export types
export type AppConfig = z.infer<typeof AppConfigSchema>;
export type AppMetadata = z.infer<typeof AppMetadataSchema>;
export type StoreApp = z.infer<typeof StoreAppSchema>;
export type InstallationProgress = z.infer<typeof InstallationProgressSchema>;
