import { z } from 'zod';

// Utility execution config schema
export const UtilityExecutionConfigSchema = z.object({
  utilityId: z.string(),
  inputFiles: z.array(z.any()).optional(),
  outputPath: z.string().optional(),
  parameters: z.record(z.any()).optional(),
  timeout: z.number().optional(),
  maxMemory: z.number().optional(),
});

// Utility result schema
export const UtilityResultSchema = z.object({
  success: z.boolean(),
  output: z.any().optional(),
  error: z.string().optional(),
  executionTime: z.number().optional(),
  memoryUsed: z.number().optional(),
  outputFiles: z.array(z.string()).optional(),
  logs: z.array(z.string()).optional(),
});

// Python execution schema
export const PythonExecutionSchema = z.object({
  pythonPath: z.string(),
  scriptPath: z.string(),
  functionName: z.string().optional(),
  args: z.array(z.any()).default([]),
  kwargs: z.record(z.any()).default({}),
  timeout: z.number().optional(),
  cwd: z.string().optional(),
  env: z.record(z.string()).optional(),
});

// Installed utility schema
export const InstalledUtilitySchema = z.object({
  id: z.string(),
  name: z.string(),
  version: z.string(),
  path: z.string(),
  config: z.any(),
  installedAt: z.date(),
  lastUsed: z.date().optional(),
  usageCount: z.number().default(0),
  runtimeType: z.enum(['python', 'python-spawn', 'wasm']),
  executablePath: z.string(),
  requirements: z.array(z.string()).optional(),
});

// Database record schema
export const DatabaseRecordSchema = z.object({
  id: z.string(),
  type: z.enum(['app', 'file', 'log', 'setting']),
  data: z.any(),
  createdAt: z.date(),
  updatedAt: z.date(),
  metadata: z.record(z.any()).optional(),
});

// API response schema
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  message: z.string().optional(),
  timestamp: z.date().optional(),
  requestId: z.string().optional(),
});

// Export types
export type UtilityExecutionConfig = z.infer<typeof UtilityExecutionConfigSchema>;
export type UtilityResult = z.infer<typeof UtilityResultSchema>;
export type PythonExecution = z.infer<typeof PythonExecutionSchema>;
export type InstalledUtility = z.infer<typeof InstalledUtilitySchema>;
export type DatabaseRecord = z.infer<typeof DatabaseRecordSchema>;
export type ApiResponse = z.infer<typeof ApiResponseSchema>;
