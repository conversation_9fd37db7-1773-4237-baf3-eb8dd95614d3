import { z } from 'zod';

// File data schema
export const FileDataSchema = z.object({
  name: z.string().min(1),
  path: z.string().optional(),
  content: z.union([z.string(), z.instanceof(ArrayBuffer), z.instanceof(Uint8Array)]),
  size: z.number().min(0),
  type: z.string().optional(),
  lastModified: z.date().optional(),
  encoding: z.string().optional(),
});

// File upload schema
export const FileUploadSchema = z.object({
  files: z.array(FileDataSchema).min(1),
  maxSize: z.number().optional(),
  allowedTypes: z.array(z.string()).optional(),
  multiple: z.boolean().default(false),
});

// File conversion result schema
export const FileConversionResultSchema = z.object({
  originalFile: FileDataSchema,
  convertedFile: FileDataSchema.optional(),
  success: z.boolean(),
  error: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  processingTime: z.number().optional(),
});

// Batch file operation schema
export const BatchFileOperationSchema = z.object({
  files: z.array(FileDataSchema),
  operation: z.string(),
  config: z.record(z.any()).optional(),
  results: z.array(FileConversionResultSchema).optional(),
  totalFiles: z.number(),
  processedFiles: z.number().default(0),
  failedFiles: z.number().default(0),
  startTime: z.date().optional(),
  endTime: z.date().optional(),
});

// File dialog options schema
export const FileDialogOptionsSchema = z.object({
  title: z.string().optional(),
  defaultPath: z.string().optional(),
  buttonLabel: z.string().optional(),
  filters: z.array(z.object({
    name: z.string(),
    extensions: z.array(z.string()),
  })).optional(),
  properties: z.array(z.enum([
    'openFile',
    'openDirectory',
    'multiSelections',
    'showHiddenFiles',
    'createDirectory',
    'promptToCreate',
    'noResolveAliases',
    'treatPackageAsDirectory',
    'dontAddToRecent'
  ])).optional(),
});

// Export types
export type FileData = z.infer<typeof FileDataSchema>;
export type FileUpload = z.infer<typeof FileUploadSchema>;
export type FileConversionResult = z.infer<typeof FileConversionResultSchema>;
export type BatchFileOperation = z.infer<typeof BatchFileOperationSchema>;
export type FileDialogOptions = z.infer<typeof FileDialogOptionsSchema>;
