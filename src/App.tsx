import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Sidebar from '@/components/Sidebar';
import HomeView from '@/components/HomeView';
import StoreView from '@/components/StoreView';
import PreferencesView from '@/components/PreferencesView';
import MyAppsView from '@/components/MyAppsView';
import UtilityRunnerView from '@/components/UtilityRunnerView';
import DebugDatabaseView from '@/components/DebugDatabaseView';
import DebugCSVToJSONView from '@/components/DebugCSVToJSONView';
import AppDetailsView from '@/components/AppDetailsView';
import SeeAllView from '@/components/SeeAllView';
import ErrorBoundary from '@/components/ErrorBoundary';
import DebugToggle from '@/components/DebugToggle';
import { StoreDataProvider } from '@/contexts/StoreDataContext';
import './index.css';

const App: React.FC = () => {
  const [sidebarVisible, setSidebarVisible] = useState<boolean>(true);

  return (
    <ErrorBoundary>
      <StoreDataProvider>
        <Router>
          <div className="flex h-screen bg-macos-bg font-system" data-testid="app-container">
            {/* Draggable title bar - only show in Electron */}
            {window.electronAPI && (
              <div className="drag-region fixed top-0 left-0 right-0 h-8 z-50" />
            )}

            {/* Sidebar with toggle functionality */}
            <div className={`${sidebarVisible ? 'w-64' : 'w-0'} flex-shrink-0 overflow-hidden`} data-testid="sidebar">
              <Sidebar
                isVisible={sidebarVisible}
                onToggle={() => setSidebarVisible(!sidebarVisible)}
              />
            </div>

            {/* Main content area */}
            <main className="flex-1 overflow-hidden relative" data-testid="main-content">
              <Routes>
                <Route path="/" element={<HomeView />} />
                <Route path="/store" element={<StoreView />} />
                <Route path="/store/app/:appId" element={<AppDetailsView />} />
                <Route path="/store/see-all/:section" element={<SeeAllView />} />
                <Route path="/preferences" element={<PreferencesView />} />
                <Route path="/my-apps" element={<MyAppsView />} />
                <Route path="/debug-database" element={<DebugDatabaseView />} />
                <Route path="/debug-csv-to-json" element={<DebugCSVToJSONView />} />
                <Route path="/app/:utilityId" element={<UtilityRunnerView />} />
              </Routes>

              {/* Debug Toggle */}
              <DebugToggle />
            </main>
          </div>
        </Router>
      </StoreDataProvider>
    </ErrorBoundary>
  );
};

export default App;
