import { Signale } from 'signale';
import chalk from 'chalk';
import logSymbols from 'log-symbols';

// Enhanced logger configuration with chalk colors and log-symbols
const options = {
  displayScope: true,
  displayBadge: true,
  displayDate: false,
  displayFilename: false,
  displayLabel: true,
  displayTimestamp: true,
  underlineLabel: false,
  underlineMessage: false,
  underlinePrefix: false,
  underlineSuffix: false,
  uppercaseLabel: false,
  types: {
    // Success and positive actions with enhanced styling
    success: {
      badge: chalk.green(logSymbols.success),
      color: 'green',
      label: chalk.green.bold('success'),
      logLevel: 'info'
    },
    complete: {
      badge: '🎉',
      color: 'green',
      label: chalk.green.bold('complete'),
      logLevel: 'info'
    },
    // Information and status
    info: {
      badge: chalk.blue(logSymbols.info),
      color: 'blue',
      label: chalk.blue.bold('info'),
      logLevel: 'info'
    },
    note: {
      badge: '📝',
      color: 'blue',
      label: chalk.blue('note'),
      logLevel: 'info'
    },
    // Process and loading states
    pending: {
      badge: '⏳',
      color: 'yellow',
      label: 'pending',
      logLevel: 'info'
    },
    await: {
      badge: '⌛',
      color: 'yellow',
      label: 'await',
      logLevel: 'info'
    },
    watch: {
      badge: '👀',
      color: 'yellow',
      label: 'watch',
      logLevel: 'info'
    },
    start: {
      badge: '🚀',
      color: 'cyan',
      label: 'start',
      logLevel: 'info'
    },
    // Warnings and cautions
    warn: {
      badge: chalk.yellow(logSymbols.warning),
      color: 'yellow',
      label: chalk.yellow.bold('warn'),
      logLevel: 'warn'
    },
    pause: {
      badge: '⏸️',
      color: 'yellow',
      label: chalk.yellow('pause'),
      logLevel: 'warn'
    },
    // Errors and failures
    error: {
      badge: chalk.red(logSymbols.error),
      color: 'red',
      label: chalk.red.bold('error'),
      logLevel: 'error'
    },
    fatal: {
      badge: '💀',
      color: 'red',
      label: chalk.red.bold('FATAL'),
      logLevel: 'error'
    },
    // Debug and development
    debug: {
      badge: '🐛',
      color: 'magenta',
      label: 'debug',
      logLevel: 'debug'
    },
    // Special FileDuck specific loggers
    fileduck: {
      badge: '🦆',
      color: 'cyan',
      label: 'fileduck',
      logLevel: 'info'
    },
    app: {
      badge: '📱',
      color: 'blue',
      label: 'app',
      logLevel: 'info'
    },
    install: {
      badge: '📦',
      color: 'green',
      label: 'install',
      logLevel: 'info'
    },
    uninstall: {
      badge: '🗑️',
      color: 'red',
      label: 'uninstall',
      logLevel: 'info'
    },
    python: {
      badge: '🐍',
      color: 'yellow',
      label: 'python',
      logLevel: 'info'
    },
    file: {
      badge: '📄',
      color: 'blue',
      label: 'file',
      logLevel: 'info'
    },
    api: {
      badge: '🌐',
      color: 'cyan',
      label: 'api',
      logLevel: 'info'
    },
    database: {
      badge: '🗄️',
      color: 'magenta',
      label: 'database',
      logLevel: 'info'
    },
    electron: {
      badge: '⚡',
      color: 'blue',
      label: 'electron',
      logLevel: 'info'
    },
    ipc: {
      badge: '🔗',
      color: 'cyan',
      label: 'ipc',
      logLevel: 'info'
    }
  }
};

// Create the main logger instance
const logger = new Signale(options);

// TypeScript-compatible exports
export {
  logger,
  Signale
};

export const createLogger = (scope: string) => logger.scope(scope);
