import { app, BrowserWindow, ipcMain, screen, protocol, shell } from 'electron';
import * as path from 'path';
import * as fs from 'fs';
import ContextBridge from './contextBridge';
import PythonRunner from './pythonRunnerSpawn';
import FileManager from './fileManager';
import { logger, createLogger } from './logger';
import type { LogLevel } from '../src/types/electron.types';

// Check if we're in development mode
const isDev: boolean = process.env.NODE_ENV === 'development' || !app.isPackaged;

// Set app name for macOS menu bar
app.setName('FileDuck');

// Handle unhandled exceptions
const electronLogger = createLogger('electron');
process.on('uncaughtException', (error: Error) => {
  electronLogger.fatal('Uncaught Exception:', error);
  if (!isDev) {
    app.quit();
  }
});

process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  if (!isDev) {
    app.quit();
  }
});

// Initialize services
let contextBridge: InstanceType<typeof ContextBridge>;
let pythonRunner: InstanceType<typeof PythonRunner>;
let fileManager: InstanceType<typeof FileManager>;

// Prevent web browser access - only allow Electron
app.commandLine.appendSwitch('disable-web-security');
if (!isDev) {
  // In production, prevent running in browser
  app.commandLine.appendSwitch('disable-features', 'VizDisplayCompositor');
}

function createWindow(): BrowserWindow {
  // Get primary display dimensions for centering
  const { width: screenWidth, height: screenHeight } = screen.getPrimaryDisplay().workAreaSize;

  const windowWidth: number = 1200;
  const windowHeight: number = 800;

  // Calculate center position
  const x: number = Math.round((screenWidth - windowWidth) / 2);
  const y: number = Math.round((screenHeight - windowHeight) / 2);

  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    x: x,
    y: y,
    minWidth: 1000,
    minHeight: 700,
    icon: isDev
      ? path.join(__dirname, '../../public/duck-icon.png')
      : path.join(process.resourcesPath, 'duck-icon.png'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'hidden',
    frame: process.platform !== 'darwin',
    show: false,
    title: 'FileDuck',
    movable: true,
    resizable: true
  });

  // Load the app
  const startUrl = isDev
    ? 'http://localhost:3000'
    : `file://${path.join(__dirname, '../build/index.html')}`;
  
  mainWindow.loadURL(startUrl);

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Don't auto-open DevTools - we'll add a toggle button

  // Handle debug console toggle
  ipcMain.handle('toggle-devtools', (): void => {
    if (mainWindow.webContents.isDevToolsOpened()) {
      mainWindow.webContents.closeDevTools();
    } else {
      mainWindow.webContents.openDevTools();
    }
  });

  // Utility management IPC handlers
  const ipcLogger = createLogger('ipc');
  ipcMain.handle('get-installed-utilities', () => {
    try {
      return contextBridge.getInstalledUtilities();
    } catch (error) {
      ipcLogger.error('Get installed utilities error:', error);
      return [];
    }
  });

  ipcMain.handle('install-utility', async (_event: Electron.IpcMainInvokeEvent, downloadUrl: string) => {
    try {
      return await contextBridge.installUtility(downloadUrl);
    } catch (error) {
      console.error('Install utility error:', error);
      throw error;
    }
  });

  ipcMain.handle('install-utility-from-local', async (_event: Electron.IpcMainInvokeEvent, localZipPath: string) => {
    try {
      return await contextBridge.installUtilityFromLocalPath(localZipPath);
    } catch (error) {
      console.error('Install utility from local error:', error);
      throw error;
    }
  });

  ipcMain.handle('uninstall-utility', async (_event: Electron.IpcMainInvokeEvent, utilityId: string) => {
    try {
      return await contextBridge.uninstallUtility(utilityId);
    } catch (error) {
      console.error('Uninstall utility error:', error);
      throw error;
    }
  });

  ipcMain.handle('run-utility', async (_event: Electron.IpcMainInvokeEvent, utilityId: string, fileData?: any[], config?: Record<string, any>) => {
    try {
      const utilityInfo = await contextBridge.runUtility(utilityId, fileData, config);

      // If this is just getting utility info (no fileData), return the info
      if (!fileData || fileData.length === 0) {
        return utilityInfo;
      }

      // Otherwise, execute the utility based on runtime type
      const pythonLogger = createLogger('python');
      switch (utilityInfo.runtimeType) {
        case 'python-spawn':
          pythonLogger.start(`Executing Python utility (spawn-based): ${utilityId}`);
          // Install dependencies first
          if (utilityInfo.config.requirements) {
            await pythonRunner.installDependencies(utilityInfo.config.requirements);
          }
          return await pythonRunner.processFileData(utilityInfo.executablePath, fileData, config);

        case 'python':
          pythonLogger.start(`Executing Python utility (legacy): ${utilityId}`);
          return await pythonRunner.processFileData(utilityInfo.executablePath, fileData, config);

        default:
          throw new Error(`Unsupported runtime type: ${utilityInfo.runtimeType}`);
      }
    } catch (error) {
      ipcLogger.error('Run utility error:', error);
      throw error;
    }
  });

  // File management IPC handlers
  ipcMain.handle('show-open-dialog', async (_event: Electron.IpcMainInvokeEvent, options?: any) => {
    try {
      return await fileManager.showOpenDialog(options);
    } catch (error) {
      ipcLogger.error('Show open dialog error:', error);
      throw error;
    }
  });

  ipcMain.handle('show-save-dialog', async (_event: Electron.IpcMainInvokeEvent, options?: any) => {
    try {
      return await fileManager.showSaveDialog(options);
    } catch (error) {
      ipcLogger.error('Show save dialog error:', error);
      throw error;
    }
  });

  ipcMain.handle('read-file', async (_event: Electron.IpcMainInvokeEvent, filePath: string) => {
    try {
      return await fileManager.readFile(filePath);
    } catch (error) {
      ipcLogger.error('Read file error:', error);
      throw error;
    }
  });

  ipcMain.handle('write-file', async (_event: Electron.IpcMainInvokeEvent, filePath: string, data: any, options?: any) => {
    try {
      return await fileManager.writeFile(filePath, data, options);
    } catch (error) {
      ipcLogger.error('Write file error:', error);
      throw error;
    }
  });

  ipcMain.handle('open-folder', async (_event: Electron.IpcMainInvokeEvent, folderPath: string) => {
    try {
      const fileLogger = createLogger('file');
      fileLogger.info(`Opening folder: ${folderPath}`);

      // Ensure the folder exists
      if (!fs.existsSync(folderPath)) {
        fileLogger.note(`Folder doesn't exist, creating: ${folderPath}`);
        fs.mkdirSync(folderPath, { recursive: true });
      }

      // Use shell.openPath which works better on macOS
      const result = await shell.openPath(folderPath);

      if (result) {
        fileLogger.error(`Failed to open folder: ${result}`);
        return { success: false, error: result };
      }

      fileLogger.success(`Successfully opened folder: ${folderPath}`);
      return { success: true };
    } catch (error: any) {
      ipcLogger.error('Open folder error:', error);
      return { success: false, error: error.message };
    }
  });

  // WASM support removed - using Python-only approach

  // Python execution IPC handlers
  ipcMain.handle('execute-python', async (_event: Electron.IpcMainInvokeEvent, pythonPath: string, functionName: string, ...args: any[]) => {
    try {
      // Note: executePythonFunction method needs to be implemented in PythonRunner
      return await (pythonRunner as any).executePythonFunction(pythonPath, functionName, ...args);
    } catch (error) {
      ipcLogger.error('Execute Python error:', error);
      throw error;
    }
  });

  ipcMain.handle('process-file-python', async (_event: Electron.IpcMainInvokeEvent, pythonPath: string, fileData: any, config?: Record<string, any>) => {
    try {
      return await pythonRunner.processFileData(pythonPath, fileData, config);
    } catch (error) {
      ipcLogger.error('Process file Python error:', error);
      throw error;
    }
  });

  ipcMain.handle('process-files-python', async (_event: Electron.IpcMainInvokeEvent, pythonPath: string, filesData: any[], config?: Record<string, any>) => {
    try {
      const pythonLogger = createLogger('python');
      pythonLogger.start(`Processing ${filesData.length} files with Python`);
      const results: any[] = [];

      for (const fileData of filesData) {
        const result = await pythonRunner.processFileData(pythonPath, fileData.content, {
          ...config,
          filename: fileData.name
        });
        results.push({
          filename: fileData.name,
          result: result
        });
      }

      return results;
    } catch (error) {
      ipcLogger.error('Process files Python error:', error);
      throw error;
    }
  });

  // Database management IPC handlers (for debug dashboard)
  ipcMain.handle('get-utility-by-id', (_event: Electron.IpcMainInvokeEvent, id: string) => {
    try {
      const utilities = contextBridge.getInstalledUtilities();
      return utilities.find((u: any) => u.id === id) || null;
    } catch (error) {
      ipcLogger.error('Get utility by ID error:', error);
      throw error;
    }
  });

  ipcMain.handle('delete-utility', async (_event: Electron.IpcMainInvokeEvent, id: string) => {
    try {
      return await contextBridge.uninstallUtility(id);
    } catch (error) {
      ipcLogger.error('Delete utility error:', error);
      throw error;
    }
  });

  ipcMain.handle('clear-database', () => {
    try {
      const userDataPath = app.getPath('userData');
      const installedJsonPath = path.join(userDataPath, 'installed.json');
      const utilitiesPath = path.join(userDataPath, 'utilities');

      // Clear database
      const initialData = { installed: [] };
      fs.writeFileSync(installedJsonPath, JSON.stringify(initialData, null, 2));

      // Remove all utility directories
      if (fs.existsSync(utilitiesPath)) {
        fs.rmSync(utilitiesPath, { recursive: true, force: true });
        fs.mkdirSync(utilitiesPath, { recursive: true });
      }

      return { success: true };
    } catch (error) {
      console.error('Clear database error:', error);
      throw error;
    }
  });

  ipcMain.handle('add-utility', (_event: Electron.IpcMainInvokeEvent, data: any) => {
    try {
      const userDataPath = app.getPath('userData');
      const installedJsonPath = path.join(userDataPath, 'installed.json');

      const installedData = (contextBridge as any).getInstalledData();
      installedData.installed.push(data);
      fs.writeFileSync(installedJsonPath, JSON.stringify(installedData, null, 2));

      return { success: true };
    } catch (error) {
      ipcLogger.error('Add utility error:', error);
      throw error;
    }
  });

  ipcMain.handle('update-utility', (_event: Electron.IpcMainInvokeEvent, id: string, data: any) => {
    try {
      const userDataPath = app.getPath('userData');
      const installedJsonPath = path.join(userDataPath, 'installed.json');

      const installedData = (contextBridge as any).getInstalledData();
      const index = installedData.installed.findIndex((u: any) => u.id === id);
      if (index !== -1) {
        installedData.installed[index] = { ...installedData.installed[index], ...data };
        fs.writeFileSync(installedJsonPath, JSON.stringify(installedData, null, 2));
        return { success: true };
      } else {
        throw new Error('Utility not found');
      }
    } catch (error) {
      console.error('Update utility error:', error);
      throw error;
    }
  });

  ipcMain.handle('get-user-data-path', (): string => {
    return app.getPath('userData');
  });

  // Logging IPC handlers - bridge React logs to Node.js console via Signale
  ipcMain.handle('log-message', (_event: Electron.IpcMainInvokeEvent, { level, scope, message, args }: {
    level: LogLevel;
    scope?: string;
    message: string;
    args?: any[];
  }) => {
    const scopedLogger = scope ? createLogger(scope) : logger;

    switch (level) {
      case 'success':
        scopedLogger.success(message, ...(args || []));
        break;
      case 'complete':
        scopedLogger.complete(message, ...(args || []));
        break;
      case 'info':
        scopedLogger.info(message, ...(args || []));
        break;
      case 'note':
        scopedLogger.note(message, ...(args || []));
        break;
      case 'pending':
        scopedLogger.pending(message, ...(args || []));
        break;
      case 'await':
        scopedLogger.await(message, ...(args || []));
        break;
      case 'watch':
        scopedLogger.watch(message, ...(args || []));
        break;
      case 'start':
        scopedLogger.start(message, ...(args || []));
        break;
      case 'warn':
        scopedLogger.warn(message, ...(args || []));
        break;
      case 'pause':
        scopedLogger.pause(message, ...(args || []));
        break;
      case 'error':
        scopedLogger.error(message, ...(args || []));
        break;
      case 'fatal':
        scopedLogger.fatal(message, ...(args || []));
        break;
      case 'debug':
        scopedLogger.debug(message, ...(args || []));
        break;
      case 'fileduck':
        scopedLogger.fileduck(message, ...(args || []));
        break;
      case 'app':
        scopedLogger.app(message, ...(args || []));
        break;
      case 'install':
        scopedLogger.install(message, ...(args || []));
        break;
      case 'uninstall':
        scopedLogger.uninstall(message, ...(args || []));
        break;
      case 'python':
        scopedLogger.python(message, ...(args || []));
        break;
      case 'file':
        scopedLogger.file(message, ...(args || []));
        break;
      case 'api':
        scopedLogger.api(message, ...(args || []));
        break;
      case 'database':
        scopedLogger.database(message, ...(args || []));
        break;
      default:
        scopedLogger.info(message, ...(args || []));
    }
  });

  return mainWindow;
}

// This method will be called when Electron has finished initialization
app.whenReady().then(async (): Promise<void> => {
  // Install DevTools extensions in development
  if (isDev) {
    try {
      const { default: installExtension, REACT_DEVELOPER_TOOLS } = await import('electron-devtools-installer');

      // Force reinstall to avoid compatibility issues
      await installExtension(REACT_DEVELOPER_TOOLS, {
        loadExtensionOptions: {
          allowFileAccess: true,
        },
        forceDownload: false
      });
      electronLogger.success('React DevTools installed successfully');
    } catch (err) {
      // Silently skip DevTools installation if it fails
      // This prevents extension-related warnings in the console
    }
  }

  // Register custom protocol for installed utilities
  protocol.registerFileProtocol('installed-utilities', (request, callback) => {
    const url = request.url.substr(20); // Remove 'installed-utilities://' prefix
    const filePath = path.join(app.getPath('userData'), 'utilities', url);
    callback({ path: filePath });
  });

  // Initialize services with error handling
  try {
    contextBridge = new ContextBridge();
    electronLogger.success('ContextBridge initialized successfully');
  } catch (error) {
    electronLogger.fatal('Failed to initialize ContextBridge:', error);
    process.exit(1);
  }

  // WASM support removed - using Python-only approach

  try {
    pythonRunner = new PythonRunner();
    electronLogger.success('PythonRunner initialized successfully');
  } catch (error) {
    electronLogger.fatal('Failed to initialize PythonRunner:', error);
    process.exit(1);
  }

  try {
    fileManager = new FileManager();
    electronLogger.success('FileManager initialized successfully');
  } catch (error) {
    electronLogger.fatal('Failed to initialize FileManager:', error);
    process.exit(1);
  }

  createWindow();
});

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', (): void => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', (): void => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (_event: Electron.Event, contents: Electron.WebContents): void => {
  contents.setWindowOpenHandler(() => {
    return { action: 'deny' };
  });
});
