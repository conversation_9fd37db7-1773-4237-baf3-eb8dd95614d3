{"name": "fileduck-desktop", "version": "0.1.0", "description": "FileDuck - Desktop utility application", "author": "FileDuck Team", "main": "electron/main.ts", "homepage": "./", "scripts": {"start": "concurrently \"pnpm run start:renderer\" \"wait-on http://localhost:3000 && pnpm run start:electron\"", "start:renderer": "BROWSER=none craco start", "start:electron": "electron .", "build": "craco build", "build:electron": "electron-builder", "dist": "pnpm run build && electron-builder --publish=never", "test": "craco test", "eject": "react-scripts eject", "type-check": "tsc --noEmit", "lint": "eslint src electron --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src electron --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx}\" \"electron/**/*.{ts,js}\""}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.0", "@tailwindcss/line-clamp": "^0.4.4", "adm-zip": "^0.5.16", "better-sqlite3": "^11.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "electron-is-dev": "^3.0.1", "electron-store": "^10.1.0", "framer-motion": "^10.0.0", "lodash": "^4.17.21", "lucide-react": "^0.263.1", "pyodide": "^0.27.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hotkeys-hook": "^5.1.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "signale": "^1.4.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.67"}, "devDependencies": {"@craco/craco": "^7.1.0", "@tailwindcss/forms": "^0.5.3", "@types/adm-zip": "^0.5.7", "@types/better-sqlite3": "^7.6.13", "@types/electron": "^1.6.12", "@types/lodash": "^4.17.18", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/signale": "^1.4.7", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "assert": "^2.1.0", "autoprefixer": "^10.4.13", "chalk": "^5.4.1", "concurrently": "^7.6.0", "constants-browserify": "^1.0.0", "electron": "^22.0.0", "electron-builder": "^24.0.0", "electron-devtools-installer": "^4.0.0", "electron-rebuild": "^3.2.9", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.0", "log-symbols": "^7.0.1", "path-browserify": "^1.0.1", "postcss": "^8.4.21", "prettier": "^3.5.3", "stream-browserify": "^3.0.0", "tailwindcss": "^3.2.0", "typescript": "^5.8.3", "util": "^0.12.5", "wait-on": "^7.0.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.fileduck.desktop", "productName": "FileDuck", "directories": {"output": "dist"}, "files": ["build/**/*", "electron/**/*", "public/duck-icon.png"], "extraResources": [{"from": "public/duck-icon.png", "to": "duck-icon.png"}], "mac": {"category": "public.app-category.productivity", "icon": "public/duck-icon.png", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "bundleVersion": "1.0.0", "minimumSystemVersion": "10.14.0", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "electron/entitlements.mac.plist", "entitlementsInherit": "electron/entitlements.mac.plist"}, "dmg": {"title": "FileDuck Installer", "icon": "public/duck-icon.png", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}], "window": {"width": 540, "height": 380}, "artifactName": "FileDuck-${version}-${arch}.${ext}"}, "win": {"target": "nsis", "icon": "public/duck-icon.png"}, "linux": {"target": "AppImage", "icon": "public/duck-icon.png"}}}